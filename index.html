<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Analyse de Tumeurs IRM</title>
  <style>
    body {
      font-family: 'Segoe UI', sans-serif;
      background-color: #f4f8fb;
      margin: 0;
      padding: 0;
    }
    header {
      background-color: #004080;
      color: white;
      padding: 1rem;
      text-align: center;
    }
    main {
      max-width: 1000px;
      margin: 2rem auto;
      padding: 1rem;
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
    }
    .card {
      background-color: white;
      padding: 1rem;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    .upload-section input[type="file"] {
      display: block;
      margin-top: 0.5rem;
    }
    .btn-analyze {
      background-color: #007bff;
      color: white;
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: 6px;
      font-size: 1rem;
      cursor: pointer;
      margin-top: 1rem;
    }
    .btn-analyze:hover {
      background-color: #0056b3;
    }
    .result-section img {
      max-width: 100%;
      border-radius: 8px;
      margin-bottom: 1rem;
    }
    .probabilities, .recommendations {
      margin-top: 1rem;
    }
    .probabilities ul, .recommendations ul {
      padding-left: 1.2rem;
    }
    @media (max-width: 600px) {
      .btn-analyze {
        width: 100%;
      }
    }
  </style>
</head>
<body>
  <header>
    <h1>Analyse IRM de Tumeurs</h1>
  </header>
  <main>
    <section class="card upload-section">
      <h2>1. Téléverser une image IRM</h2>
      <input type="file" id="imageUpload" accept="image/*">
      <button class="btn-analyze" onclick="analyzeImage()">Lancer l'analyse</button>
    </section>

    <section id="resultSection" class="card result-section" style="display:none;">
      <h2>2. Résultats de l’analyse</h2>
      <div>
        <img id="uploadedImage" src="" alt="IRM avec annotation">
      </div>
      <div class="diagnostic">
        <strong>Diagnostic :</strong> <span id="diagnostic">-</span>
      </div>
      <div class="probabilities">
        <strong>Probabilités :</strong>
        <ul id="probabilityList"></ul>
      </div>
      <div class="recommendations">
        <strong>Recommandations :</strong>
        <ul id="recommendationsList"></ul>
      </div>
    </section>
  </main>

  <script>
    function analyzeImage() {
      const imageInput = document.getElementById('imageUpload');
      const resultSection = document.getElementById('resultSection');
      const uploadedImage = document.getElementById('uploadedImage');
      const diagnostic = document.getElementById('diagnostic');
      const probabilityList = document.getElementById('probabilityList');
      const recommendationsList = document.getElementById('recommendationsList');

      if (imageInput.files.length === 0) {
        alert("Veuillez sélectionner une image IRM.");
        return;
      }

      const file = imageInput.files[0];
      const reader = new FileReader();

      reader.onload = function(e) {
        uploadedImage.src = e.target.result;
        // Simuler les résultats (à remplacer par appel API IA réel)
        diagnostic.innerText = "Tumeur maligne suspectée";

        const probabilities = [
          { type: "Glioblastome", percent: 78 },
          { type: "Méningiome", percent: 15 },
          { type: "Tumeur bénigne", percent: 7 }
        ];

        const recommendations = [
          "Biopsie recommandée",
          "Suivi IRM dans 1 mois",
          "Consultation avec oncologue"
        ];

        probabilityList.innerHTML = "";
        probabilities.forEach(p => {
          const li = document.createElement('li');
          li.textContent = `${p.type} : ${p.percent}%`;
          probabilityList.appendChild(li);
        });

        recommendationsList.innerHTML = "";
        recommendations.forEach(r => {
          const li = document.createElement('li');
          li.textContent = r;
          recommendationsList.appendChild(li);
        });

        resultSection.style.display = 'block';
      };

      reader.readAsDataURL(file);
    }
  </script>
</body>
</html>
